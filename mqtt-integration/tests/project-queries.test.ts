import { describe, it, expect } from "bun:test";
import { listProjectsByLICIdentifier } from "../src/db/queries/project-queries";
import { runInTransaction } from "./helpers/db";
import {
  insertAccount,
  insertProperty,
  insertDevice,
  insertPropertyDevice,
  insertProject,
  insertUser,
  insertWaterPump,
} from "./helpers/fixtures";

describe("project-queries", () => {
  describe("listProjectsByLICIdentifier", () => {
    it("should return projects for LIC device associated and active at reference date", async () => {
      await runInTransaction(async (trx) => {
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");
        const device = await insertDevice(trx, "LIC", "LIC001");
        const startDate = new Date("2024-01-01");
        const endDate = new Date("2024-12-31");
        await insertPropertyDevice(
          trx,
          device.id,
          property.id,
          startDate,
          endDate
        );
        const waterPump = await insertWaterPump(trx, property.id);
        const project = await insertProject(
          trx,
          property.id,
          device.id,
          waterPump.id,
          startDate,
          endDate
        );

        const result = await listProjectsByLICIdentifier(
          trx,
          "LIC001",
          new Date("2024-06-01")
        );
        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
        expect(result.length).toBeGreaterThan(0);
        expect(result[0]).toBeDefined();
        expect(result[0]?.id).toBe(project.id);
      }).catch((error) => {
        console.error("Test error:", error);
      });
    });

    it("should return empty array when device is not associated at reference date", async () => {
      await runInTransaction(async (trx) => {
        const result = await listProjectsByLICIdentifier(
          trx,
          "NONEXISTENTLIC",
          new Date()
        );
        expect(result).toEqual([]);
      });
    });

    it("should return empty array when project is not active at reference date", async () => {
      await runInTransaction(async (trx) => {
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");
        const device = await insertDevice(trx, "LIC", "LIC002");
        const startDate = new Date("2024-01-01");
        const endDate = new Date("2024-12-31");
        await insertPropertyDevice(
          trx,
          device.id,
          property.id,
          startDate,
          endDate
        );
        const waterPump = await insertWaterPump(trx, property.id);
        await insertProject(
          trx,
          property.id,
          device.id,
          waterPump.id,
          startDate,
          endDate
        );

        // Test with date outside project period
        const result = await listProjectsByLICIdentifier(
          trx,
          "LIC002",
          new Date("2023-01-01")
        );
        expect(result).toEqual([]);
      });
    });

    it("should handle null end dates for ongoing associations and projects", async () => {
      await runInTransaction(async (trx) => {
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");
        const device = await insertDevice(trx, "LIC", "LIC003");
        const startDate = new Date("2024-01-01");
        await insertPropertyDevice(
          trx,
          device.id,
          property.id,
          startDate,
          null
        );
        const waterPump = await insertWaterPump(trx, property.id);
        const project = await insertProject(
          trx,
          property.id,
          device.id,
          waterPump.id,
          startDate,
          null
        );

        const result = await listProjectsByLICIdentifier(
          trx,
          "LIC003",
          new Date("2025-01-01")
        );
        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
        expect(result.length).toBeGreaterThan(0);
        expect(result[0]?.id).toBe(project.id);
      });
    });
  });
});
