import { codec } from "proto";
import type { SQL } from "bun";
import { listFullProjectsByLICIdentifier, listProjectsByLICIdentifier } from "../../db/queries/project-queries";

export async function buildConfig(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date = new Date()
): Promise<codec.in_.devices.DevicesPackage> {
  // Projects can be mapped to "group" in the DevicesPackage
  const data: codec.in_.devices.IDevicesData[] = [];
  const projects = await listFullProjectsByLICIdentifier(
    db,
    licIdentifier,
    referenceDate
  );
  for (const project of projects) {
    console.log("Project found:", project);
    project.irrigation_water_pump.
  }
  const devicesPackage = codec.in_.devices.DevicesPackage.create({
    data,
  });

  // Return the constructed DevicesPackage
  return devicesPackage;
}
