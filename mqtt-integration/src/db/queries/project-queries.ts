import type { SQL } from "bun";
import type { Project, ProjectFull } from "./types";

/**
 * Returns all projects whose `localized_irrigation_controller` has the LIC identifier
 * and is active at the reference date
 * and belongs to to a property that which has a property_device record for the device identifier and is active at the reference date
 * ordered by creation date.
 * @param db
 * @param licIdentifier
 * @param referenceDate
 * @returns
 */
export async function listProjectsByLICIdentifier(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date = new Date()
): Promise<Project[]> {
  return await db<Project[]>`
    SELECT p.*
    FROM project p
    JOIN property_device pd ON p.property = pd.property
    JOIN device d ON pd.device = d.id
    WHERE d.identifier = ${licIdentifier}
      AND d.model = 'LIC'
      AND ${referenceDate} >= COALESCE(pd.start_date, '-infinity'::timestamp)
      AND ${referenceDate} <= COALESCE(pd.end_date, 'infinity'::timestamp)
      AND p.localized_irrigation_controller = d.id
      AND ${referenceDate} >= COALESCE(p.start_date, '-infinity'::timestamp)
      AND ${referenceDate} <= COALESCE(p.end_date, 'infinity'::timestamp)
    ORDER BY p.date_created ASC
  `;
}

/**
 * Returns all projects tree (full projects) whose `localized_irrigation_controller` has the LIC identifier
 * and is active at the reference date
 * and belongs to to a property that which has a property_device record for the device identifier and is active at the reference date
 * ordered by creation date.
 * @param db
 * @param licIdentifier
 * @param referenceDate
 * @returns
 */
export async function listFullProjectsByLICIdentifier(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date = new Date()
): Promise<ProjectFull[]> {
  return await db<ProjectFull[]>`
    SELECT 
      p.*,
      -- Localized Irrigation Controller (LIC)
      jsonb_build_object(
        'id', lic_device.id,
        'identifier', lic_device.identifier,
        'model', lic_device.model,
        'date_created', lic_device.date_created,
        'user_created', lic_device.user_created,
        'date_updated', lic_device.date_updated,
        'user_updated', lic_device.user_updated,
        'metadata', lic_device.metadata,
        'notes', lic_device.notes
      ) as localized_irrigation_controller,
      
      -- Irrigation Water Pump with Controller
      CASE 
        WHEN iwp.id IS NOT NULL THEN
          jsonb_build_object(
            'id', iwp.id,
            'property', iwp.property,
            'water_pump_controller', iwp.water_pump_controller,
            'label', iwp.label,
            'identifier', iwp.identifier,
            'pump_type', iwp.pump_type,
            'pump_model', iwp.pump_model,
            'has_frequency_inverter', iwp.has_frequency_inverter,
            'monitor_operation', iwp.monitor_operation,
            'flow_rate_lh', iwp.flow_rate_lh,
            'notes', iwp.notes,
            'metadata', iwp.metadata,
            'date_created', iwp.date_created,
            'user_created', iwp.user_created,
            'date_updated', iwp.date_updated,
            'user_updated', iwp.user_updated,
            'controller', jsonb_build_object(
              'id', iwp_controller.id,
              'identifier', iwp_controller.identifier,
              'model', iwp_controller.model,
              'date_created', iwp_controller.date_created,
              'user_created', iwp_controller.user_created,
              'date_updated', iwp_controller.date_updated,
              'user_updated', iwp_controller.user_updated,
              'metadata', iwp_controller.metadata,
              'notes', iwp_controller.notes
            )
          )
        ELSE NULL
      END as irrigation_water_pump,
      
      -- Fertigation Water Pump with Controller
      CASE 
        WHEN fwp.id IS NOT NULL THEN
          jsonb_build_object(
            'id', fwp.id,
            'property', fwp.property,
            'water_pump_controller', fwp.water_pump_controller,
            'label', fwp.label,
            'identifier', fwp.identifier,
            'pump_type', fwp.pump_type,
            'pump_model', fwp.pump_model,
            'has_frequency_inverter', fwp.has_frequency_inverter,
            'monitor_operation', fwp.monitor_operation,
            'flow_rate_lh', fwp.flow_rate_lh,
            'notes', fwp.notes,
            'metadata', fwp.metadata,
            'date_created', fwp.date_created,
            'user_created', fwp.user_created,
            'date_updated', fwp.date_updated,
            'user_updated', fwp.user_updated,
            'controller', jsonb_build_object(
              'id', fwp_controller.id,
              'identifier', fwp_controller.identifier,
              'model', fwp_controller.model,
              'date_created', fwp_controller.date_created,
              'user_created', fwp_controller.user_created,
              'date_updated', fwp_controller.date_updated,
              'user_updated', fwp_controller.user_updated,
              'metadata', fwp_controller.metadata,
              'notes', fwp_controller.notes
            )
          )
        ELSE NULL
      END as fertigation_water_pump,
      
      -- Sectors with Valve Controllers
      COALESCE(
        jsonb_agg(
          DISTINCT jsonb_build_object(
            'id', s.id,
            'project', s.project,
            'name', s.name,
            'valve_controller', s.valve_controller,
            'valve_controller_output', s.valve_controller_output,
            'description', s.description,
            'date_created', s.date_created,
            'user_created', s.user_created,
            'date_updated', s.date_updated,
            'user_updated', s.user_updated,
            'metadata', s.metadata,
            'notes', s.notes,
            'valve_controller_device', jsonb_build_object(
              'id', vc_device.id,
              'identifier', vc_device.identifier,
              'model', vc_device.model,
              'date_created', vc_device.date_created,
              'user_created', vc_device.user_created,
              'date_updated', vc_device.date_updated,
              'user_updated', vc_device.user_updated,
              'metadata', vc_device.metadata,
              'notes', vc_device.notes
            )
          )
        ) FILTER (WHERE s.id IS NOT NULL),
        '[]'::jsonb
      ) as sectors
    
    FROM project p
    
    -- Join with property_device to get the LIC device
    JOIN property_device lic_pd ON p.property = lic_pd.property
    JOIN device lic_device ON lic_pd.device = lic_device.id
    
    -- Left join with irrigation water pump and its controller
    LEFT JOIN water_pump iwp ON p.irrigation_water_pump = iwp.id
    LEFT JOIN device iwp_controller ON iwp.water_pump_controller = iwp_controller.id
      AND iwp_controller.model IN ('WPC-PL10', 'WPC-PL50')
    
    -- Left join with fertigation water pump and its controller
    LEFT JOIN water_pump fwp ON p.fertigation_water_pump = fwp.id
    LEFT JOIN device fwp_controller ON fwp.water_pump_controller = fwp_controller.id
      AND fwp_controller.model IN ('WPC-PL10', 'WPC-PL50')
    
    -- Left join with sectors and their valve controllers
    LEFT JOIN sector s ON p.id = s.project
    LEFT JOIN property_device vc_pd ON s.valve_controller = vc_pd.device
      AND vc_pd.property = p.property
      AND ${referenceDate} >= COALESCE(vc_pd.start_date, '-infinity'::timestamp)
      AND ${referenceDate} <= COALESCE(vc_pd.end_date, 'infinity'::timestamp)
    LEFT JOIN device vc_device ON vc_pd.device = vc_device.id
      AND vc_device.model = 'VC'
    
    WHERE lic_device.identifier = ${licIdentifier}
      AND lic_device.model = 'LIC'
      AND ${referenceDate} >= COALESCE(lic_pd.start_date, '-infinity'::timestamp)
      AND ${referenceDate} <= COALESCE(lic_pd.end_date, 'infinity'::timestamp)
      AND p.localized_irrigation_controller = lic_device.id
      AND ${referenceDate} >= COALESCE(p.start_date, '-infinity'::timestamp)
      AND ${referenceDate} <= COALESCE(p.end_date, 'infinity'::timestamp)
    
    GROUP BY 
      p.id, p.property, p.name, p.irrigation_water_pump, p.fertigation_water_pump,
      p.localized_irrigation_controller, p.description, p.start_date, p.end_date,
      p.date_created, p.user_created, p.date_updated, p.user_updated, p.metadata, p.notes,
      lic_device.id, lic_device.identifier, lic_device.model, lic_device.date_created,
      lic_device.user_created, lic_device.date_updated, lic_device.user_updated,
      lic_device.metadata, lic_device.notes,
      iwp.id, iwp.property, iwp.water_pump_controller, iwp.label, iwp.identifier, 
      iwp.pump_type, iwp.pump_model, iwp.has_frequency_inverter, iwp.monitor_operation,
      iwp.flow_rate_lh, iwp.notes, iwp.metadata, iwp.date_created, iwp.user_created, 
      iwp.date_updated, iwp.user_updated,
      iwp_controller.id, iwp_controller.identifier, iwp_controller.model,
      iwp_controller.date_created, iwp_controller.user_created, iwp_controller.date_updated,
      iwp_controller.user_updated, iwp_controller.metadata, iwp_controller.notes,
      fwp.id, fwp.property, fwp.water_pump_controller, fwp.label, fwp.identifier,
      fwp.pump_type, fwp.pump_model, fwp.has_frequency_inverter, fwp.monitor_operation,
      fwp.flow_rate_lh, fwp.notes, fwp.metadata, fwp.date_created, fwp.user_created, 
      fwp.date_updated, fwp.user_updated,
      fwp_controller.id, fwp_controller.identifier, fwp_controller.model,
      fwp_controller.date_created, fwp_controller.user_created, fwp_controller.date_updated,
      fwp_controller.user_updated, fwp_controller.metadata, fwp_controller.notes
    
    ORDER BY p.date_created ASC
  `;
}
