// Base types for database entities

export type Project = {
  id: string;
  property: string;
  name: string;
  irrigation_water_pump: string;
  fertigation_water_pump: string | null;
  localized_irrigation_controller: string;
  description: string | null;
  start_date: string | null;
  end_date: string | null;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any;
  notes: string | null;
};

export type Device = {
  id: string;
  identifier: string;
  model: string;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any;
  notes: string | null;
};

export type WaterPump = {
  id: string;
  property: string;
  water_pump_controller: string | null;
  label: string;
  identifier: string;
  pump_type: "IRRIGATION" | "FERTIGATION" | "SERVICE";
  pump_model: string | null;
  has_frequency_inverter: boolean;
  monitor_operation: boolean;
  flow_rate_lh: number | null;
  notes: string | null;
  metadata: any;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
};

export type Sector = {
  id: string;
  project: string;
  name: string;
  valve_controller: string;
  valve_controller_output: number;
  description: string | null;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any;
  notes: string | null;
};

export type IrrigationPlan = {
  id: string;
  project: string;
  name: string;
  description: string | null;
  total_irrigation_duration_seconds: number | null;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any;
  notes: string | null;
};

export type IrrigationPlanStep = {
  id: string;
  irrigation_plan: string;
  step_order: number;
  sector: string;
  duration_seconds: number;
  fertigation_duration_seconds: number | null;
  description: string | null;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any;
  notes: string | null;
};

// Composite types for queries with joined data

export type WaterPumpWithController = WaterPump & {
  controller: Device;
};

export type SectorWithValveController = Sector & {
  valve_controller_device: Device;
};

export type IrrigationPlanWithSteps = IrrigationPlan & {
  steps: IrrigationPlanStep[];
};

// Full project type with all related data (excluding irrigation plans)
export type ProjectFull = Project & {
  sectors: SectorWithValveController[];
  irrigation_water_pump: WaterPumpWithController | null;
  fertigation_water_pump: WaterPumpWithController | null;
  localized_irrigation_controller: Device;
};
