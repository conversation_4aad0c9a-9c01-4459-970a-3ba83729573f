# Task list info:

- name: 250804_02
- base_branch: develop

---

# Tasks

## Task 1. LIC WIFI configuration should be stored in property_device metadata instead of device metadata

**Description**
The LIC WIFI configuration (SSID and password) is currently stored in the device metadata. This is incorrect because the same LIC can be associated with different properties and thus have different WIFI configurations. The WIFI configuration must be stored in the property_device metadata instead.
if WIFI configuration has changed, updateProperty<PERSON>ev<PERSON><PERSON><PERSON> must be called, instead of updateDev<PERSON><PERSON><PERSON>, in order to update the WIFI configuration in app/src/pages/main/components/DeviceDetailModal.tsx.

**Target directories**

- app (frontend)
- mqtt-integration (backend)

**Status:** Done

## Task 2. property_device label should be editable in the frontend

**Description**
The property_device label is currently not editable in the frontend. It should be editable.
A new input field should be added to app/src/pages/main/components/DeviceDetailModal.tsx to edit the label. The label is a nullabe existing field in the property_device table.
If the label is changed, updatePropertyDev<PERSON><PERSON><PERSON> must be called in order to update the label in the database.

**Target directories**

- app (frontend)

**Status:** Done

## Task 3. Mesh network should validation when configuring Reservoirs and Projects

**Description**

- When configuring Reservoirs, the user should only be able to select Reservoir Monitors and Service Water Pumps that are in the same mesh network (mesh_device_mapping).
- When configuring Projects, the user should only be able to select Irrigation Water Pumps, Fertigation Water Pumps and Valve Controllers that are mapped to the Project's LIC (mesh_device_mapping).
- The frontend should filter the available devices based on the above rules, and the database should enforce them via triggers.

**Target directories**

- app (frontend)
- directus (backend)

**Status:** Pending
